//+------------------------------------------------------------------+
//|                                              OrderManager.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef ORDER_MANAGER_MQH
#define ORDER_MANAGER_MQH

#include "../../Base/BaseComponent.mqh"

//+------------------------------------------------------------------+
//| Order Result Structure                                           |
//+------------------------------------------------------------------+
struct <PERSON><PERSON><PERSON><PERSON>
{
    bool              success;          // Operation success
    int               ticket;           // Order ticket
    double            price;            // Execution price
    double            lotSize;          // Executed lot size
    string            errorMessage;     // Error message if failed
    int               errorCode;        // Error code if failed
};

//+------------------------------------------------------------------+
//| OrderManager Class                                               |
//| Implementation of order execution and management system         |
//+------------------------------------------------------------------+
class OrderManager : public BaseComponent
{
private:
    string            m_symbol;             // Trading symbol
    int               m_magicNumber;        // Magic number
    int               m_slippage;           // Allowed slippage
    int               m_maxRetries;         // Maximum retry attempts
    int               m_retryDelay;         // Delay between retries (ms)
    
    // Order tracking
    int               m_lastTicket;         // Last order ticket
    double            m_lastPrice;          // Last execution price
    datetime          m_lastOrderTime;      // Last order time

public:
    //--- Constructor and Destructor
                      OrderManager(string symbol = "", int magicNumber = 0, int slippage = 3);
    virtual          ~OrderManager();
    
    //--- Configuration methods
    void              SetSymbol(string symbol) { m_symbol = symbol; }
    void              SetMagicNumber(int magic) { m_magicNumber = magic; }
    void              SetSlippage(int slippage) { m_slippage = MathMax(0, slippage); }
    void              SetMaxRetries(int retries) { m_maxRetries = MathMax(1, retries); }
    void              SetRetryDelay(int delay) { m_retryDelay = MathMax(100, delay); }
    
    //--- Information methods
    string            GetSymbol() const { return m_symbol; }
    int               GetMagicNumber() const { return m_magicNumber; }
    int               GetSlippage() const { return m_slippage; }
    int               GetLastTicket() const { return m_lastTicket; }
    double            GetLastPrice() const { return m_lastPrice; }
    
    //--- Order execution methods
    OrderResult       OpenBuyOrder(double lotSize, double stopLoss = 0.0, double takeProfit = 0.0, string comment = "");
    OrderResult       OpenSellOrder(double lotSize, double stopLoss = 0.0, double takeProfit = 0.0, string comment = "");
    OrderResult       OpenOrder(int orderType, double lotSize, double price, double stopLoss = 0.0, 
                                double takeProfit = 0.0, string comment = "");
    
    //--- Order modification methods
    bool              ModifyOrder(int ticket, double stopLoss, double takeProfit);
    bool              ModifyOrderPrice(int ticket, double newPrice);
    bool              ModifyStopLoss(int ticket, double stopLoss);
    bool              ModifyTakeProfit(int ticket, double takeProfit);
    
    //--- Order closure methods
    bool              CloseOrder(int ticket, double lotSize = 0.0);
    bool              CloseAllOrders();
    bool              CloseOrdersByType(int orderType);
    bool              CloseOrdersByMagic(int magicNumber);
    
    //--- Order information methods
    bool              IsOrderOpen(int ticket);
    double            GetOrderProfit(int ticket);
    double            GetOrderLots(int ticket);
    int               GetOrderType(int ticket);
    double            GetOrderOpenPrice(int ticket);
    datetime          GetOrderOpenTime(int ticket);
    
    //--- Position management methods
    int               CountOpenOrders();
    int               CountOrdersByType(int orderType);
    double            GetTotalLotSize();
    double            GetTotalProfit();
    
    //--- Override base class methods
    virtual bool      OnInitialize() override;
    virtual bool      OnValidate() override;
    
    //--- Utility methods
    bool              IsValidLotSize(double lotSize);
    double            NormalizeLotSize(double lotSize);
    bool              IsValidPrice(double price);
    double            NormalizePrice(double price);
    bool              IsMarketOpen();
    string            OrderTypeToString(int orderType);
    void              LogOrderResult(const OrderResult& result);
    
private:
    //--- Internal methods
    OrderResult       ExecuteOrderWithRetry(int orderType, double lotSize, double price, 
                                           double stopLoss, double takeProfit, string comment);
    bool              WaitForOrderExecution(int ticket, int timeoutMs = 5000);
    void              HandleOrderError(int errorCode, OrderResult& result);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
OrderManager::OrderManager(string symbol = "", int magicNumber = 0, int slippage = 3) : BaseComponent("OrderManager")
{
    m_symbol = (symbol == "") ? Symbol() : symbol;
    m_magicNumber = magicNumber;
    m_slippage = slippage;
    m_maxRetries = 3;
    m_retryDelay = 1000;
    
    m_lastTicket = -1;
    m_lastPrice = 0.0;
    m_lastOrderTime = 0;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
OrderManager::~OrderManager()
{
    // Cleanup handled by base class
}

//+------------------------------------------------------------------+
//| Initialize order manager                                         |
//+------------------------------------------------------------------+
bool OrderManager::OnInitialize()
{
    if (m_symbol == "")
    {
        SetError(1201, "Invalid symbol for order management");
        return false;
    }
    
    if (m_magicNumber <= 0)
    {
        SetError(1202, "Invalid magic number");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate parameters                                              |
//+------------------------------------------------------------------+
bool OrderManager::OnValidate()
{
    if (!IsMarketOpen())
    {
        SetError(1203, "Market is closed");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Open buy order                                                   |
//+------------------------------------------------------------------+
OrderResult OrderManager::OpenBuyOrder(double lotSize, double stopLoss = 0.0, double takeProfit = 0.0, string comment = "")
{
    double price = MarketInfo(m_symbol, MODE_ASK);
    return ExecuteOrderWithRetry(OP_BUY, lotSize, price, stopLoss, takeProfit, comment);
}

//+------------------------------------------------------------------+
//| Open sell order                                                  |
//+------------------------------------------------------------------+
OrderResult OrderManager::OpenSellOrder(double lotSize, double stopLoss = 0.0, double takeProfit = 0.0, string comment = "")
{
    double price = MarketInfo(m_symbol, MODE_BID);
    return ExecuteOrderWithRetry(OP_SELL, lotSize, price, stopLoss, takeProfit, comment);
}

//+------------------------------------------------------------------+
//| Open order with specified parameters                            |
//+------------------------------------------------------------------+
OrderResult OrderManager::OpenOrder(int orderType, double lotSize, double price, double stopLoss = 0.0, 
                                    double takeProfit = 0.0, string comment = "")
{
    return ExecuteOrderWithRetry(orderType, lotSize, price, stopLoss, takeProfit, comment);
}

//+------------------------------------------------------------------+
//| Execute order with retry mechanism                              |
//+------------------------------------------------------------------+
OrderResult OrderManager::ExecuteOrderWithRetry(int orderType, double lotSize, double price, 
                                                double stopLoss, double takeProfit, string comment)
{
    OrderResult result;
    result.success = false;
    result.ticket = -1;
    result.price = 0.0;
    result.lotSize = 0.0;
    result.errorMessage = "";
    result.errorCode = 0;
    
    // Validate parameters
    if (!IsValidLotSize(lotSize))
    {
        result.errorMessage = "Invalid lot size";
        result.errorCode = ERR_INVALID_TRADE_VOLUME;
        return result;
    }
    
    if (!IsValidPrice(price))
    {
        result.errorMessage = "Invalid price";
        result.errorCode = ERR_INVALID_PRICE;
        return result;
    }
    
    // Normalize values
    lotSize = NormalizeLotSize(lotSize);
    price = NormalizePrice(price);
    stopLoss = (stopLoss > 0.0) ? NormalizePrice(stopLoss) : 0.0;
    takeProfit = (takeProfit > 0.0) ? NormalizePrice(takeProfit) : 0.0;
    
    // Attempt order execution with retries
    for (int attempt = 0; attempt < m_maxRetries; attempt++)
    {
        if (attempt > 0)
        {
            Sleep(m_retryDelay);
            
            // Update price for market orders
            if (orderType == OP_BUY)
                price = MarketInfo(m_symbol, MODE_ASK);
            else if (orderType == OP_SELL)
                price = MarketInfo(m_symbol, MODE_BID);
        }
        
        int ticket = OrderSend(m_symbol, orderType, lotSize, price, m_slippage, 
                              stopLoss, takeProfit, comment, m_magicNumber, 0, clrNONE);
        
        if (ticket > 0)
        {
            result.success = true;
            result.ticket = ticket;
            result.lotSize = lotSize;
            
            // Get actual execution price
            if (OrderSelect(ticket, SELECT_BY_TICKET))
            {
                result.price = OrderOpenPrice();
            }
            else
            {
                result.price = price;
            }
            
            m_lastTicket = ticket;
            m_lastPrice = result.price;
            m_lastOrderTime = TimeCurrent();
            
            LogOrderResult(result);
            return result;
        }
        else
        {
            int errorCode = GetLastError();
            HandleOrderError(errorCode, result);
            
            // Break on non-retryable errors
            if (errorCode == ERR_NOT_ENOUGH_MONEY || 
                errorCode == ERR_TRADE_NOT_ALLOWED ||
                errorCode == ERR_INVALID_TRADE_VOLUME)
            {
                break;
            }
        }
    }
    
    LogOrderResult(result);
    return result;
}

//+------------------------------------------------------------------+
//| Modify order                                                     |
//+------------------------------------------------------------------+
bool OrderManager::ModifyOrder(int ticket, double stopLoss, double takeProfit)
{
    if (!OrderSelect(ticket, SELECT_BY_TICKET))
        return false;
    
    double openPrice = OrderOpenPrice();
    stopLoss = (stopLoss > 0.0) ? NormalizePrice(stopLoss) : 0.0;
    takeProfit = (takeProfit > 0.0) ? NormalizePrice(takeProfit) : 0.0;
    
    return OrderModify(ticket, openPrice, stopLoss, takeProfit, 0, clrNONE);
}

//+------------------------------------------------------------------+
//| Modify stop loss                                                 |
//+------------------------------------------------------------------+
bool OrderManager::ModifyStopLoss(int ticket, double stopLoss)
{
    if (!OrderSelect(ticket, SELECT_BY_TICKET))
        return false;
    
    return ModifyOrder(ticket, stopLoss, OrderTakeProfit());
}

//+------------------------------------------------------------------+
//| Modify take profit                                               |
//+------------------------------------------------------------------+
bool OrderManager::ModifyTakeProfit(int ticket, double takeProfit)
{
    if (!OrderSelect(ticket, SELECT_BY_TICKET))
        return false;
    
    return ModifyOrder(ticket, OrderStopLoss(), takeProfit);
}

//+------------------------------------------------------------------+
//| Close order                                                      |
//+------------------------------------------------------------------+
bool OrderManager::CloseOrder(int ticket, double lotSize = 0.0)
{
    if (!OrderSelect(ticket, SELECT_BY_TICKET))
        return false;
    
    if (OrderCloseTime() > 0)
        return true; // Already closed
    
    double lots = (lotSize > 0.0) ? lotSize : OrderLots();
    double price = (OrderType() == OP_BUY) ? MarketInfo(m_symbol, MODE_BID) : MarketInfo(m_symbol, MODE_ASK);
    
    return OrderClose(ticket, lots, price, m_slippage, clrNONE);
}

//+------------------------------------------------------------------+
//| Close all orders                                                 |
//+------------------------------------------------------------------+
bool OrderManager::CloseAllOrders()
{
    bool allClosed = true;
    
    for (int i = OrdersTotal() - 1; i >= 0; i--)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if (OrderSymbol() == m_symbol && OrderMagicNumber() == m_magicNumber)
            {
                if (!CloseOrder(OrderTicket()))
                {
                    allClosed = false;
                }
            }
        }
    }
    
    return allClosed;
}

//+------------------------------------------------------------------+
//| Close orders by type                                             |
//+------------------------------------------------------------------+
bool OrderManager::CloseOrdersByType(int orderType)
{
    bool allClosed = true;
    
    for (int i = OrdersTotal() - 1; i >= 0; i--)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if (OrderSymbol() == m_symbol && 
                OrderMagicNumber() == m_magicNumber && 
                OrderType() == orderType)
            {
                if (!CloseOrder(OrderTicket()))
                {
                    allClosed = false;
                }
            }
        }
    }
    
    return allClosed;
}

//+------------------------------------------------------------------+
//| Count open orders                                                |
//+------------------------------------------------------------------+
int OrderManager::CountOpenOrders()
{
    int count = 0;
    
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if (OrderSymbol() == m_symbol && OrderMagicNumber() == m_magicNumber)
            {
                count++;
            }
        }
    }
    
    return count;
}

//+------------------------------------------------------------------+
//| Count orders by type                                             |
//+------------------------------------------------------------------+
int OrderManager::CountOrdersByType(int orderType)
{
    int count = 0;
    
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if (OrderSymbol() == m_symbol && 
                OrderMagicNumber() == m_magicNumber && 
                OrderType() == orderType)
            {
                count++;
            }
        }
    }
    
    return count;
}

//+------------------------------------------------------------------+
//| Get total lot size                                               |
//+------------------------------------------------------------------+
double OrderManager::GetTotalLotSize()
{
    double totalLots = 0.0;
    
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if (OrderSymbol() == m_symbol && OrderMagicNumber() == m_magicNumber)
            {
                totalLots += OrderLots();
            }
        }
    }
    
    return totalLots;
}

//+------------------------------------------------------------------+
//| Get total profit                                                 |
//+------------------------------------------------------------------+
double OrderManager::GetTotalProfit()
{
    double totalProfit = 0.0;
    
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if (OrderSymbol() == m_symbol && OrderMagicNumber() == m_magicNumber)
            {
                totalProfit += OrderProfit() + OrderSwap() + OrderCommission();
            }
        }
    }
    
    return totalProfit;
}

//+------------------------------------------------------------------+
//| Check if order is open                                           |
//+------------------------------------------------------------------+
bool OrderManager::IsOrderOpen(int ticket)
{
    return (OrderSelect(ticket, SELECT_BY_TICKET) && OrderCloseTime() == 0);
}

//+------------------------------------------------------------------+
//| Validate lot size                                                |
//+------------------------------------------------------------------+
bool OrderManager::IsValidLotSize(double lotSize)
{
    double minLot = MarketInfo(m_symbol, MODE_MINLOT);
    double maxLot = MarketInfo(m_symbol, MODE_MAXLOT);
    
    return (lotSize >= minLot && lotSize <= maxLot);
}

//+------------------------------------------------------------------+
//| Normalize lot size                                               |
//+------------------------------------------------------------------+
double OrderManager::NormalizeLotSize(double lotSize)
{
    double minLot = MarketInfo(m_symbol, MODE_MINLOT);
    double maxLot = MarketInfo(m_symbol, MODE_MAXLOT);
    double lotStep = MarketInfo(m_symbol, MODE_LOTSTEP);
    
    lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
    
    if (lotStep > 0.0)
    {
        lotSize = MathRound(lotSize / lotStep) * lotStep;
    }
    
    return lotSize;
}

//+------------------------------------------------------------------+
//| Validate price                                                   |
//+------------------------------------------------------------------+
bool OrderManager::IsValidPrice(double price)
{
    return (price > 0.0 && price != EMPTY_VALUE);
}

//+------------------------------------------------------------------+
//| Normalize price                                                  |
//+------------------------------------------------------------------+
double OrderManager::NormalizePrice(double price)
{
    return NormalizeDouble(price, Digits);
}

//+------------------------------------------------------------------+
//| Check if market is open                                          |
//+------------------------------------------------------------------+
bool OrderManager::IsMarketOpen()
{
    return (MarketInfo(m_symbol, MODE_TRADEALLOWED) > 0);
}

//+------------------------------------------------------------------+
//| Convert order type to string                                     |
//+------------------------------------------------------------------+
string OrderManager::OrderTypeToString(int orderType)
{
    switch(orderType)
    {
        case OP_BUY:        return "BUY";
        case OP_SELL:       return "SELL";
        case OP_BUYLIMIT:   return "BUY LIMIT";
        case OP_SELLLIMIT:  return "SELL LIMIT";
        case OP_BUYSTOP:    return "BUY STOP";
        case OP_SELLSTOP:   return "SELL STOP";
        default:            return "UNKNOWN";
    }
}

//+------------------------------------------------------------------+
//| Log order result                                                 |
//+------------------------------------------------------------------+
void OrderManager::LogOrderResult(const OrderResult& result)
{
    if (result.success)
    {
        Print("ORDER SUCCESS: Ticket=", result.ticket, 
              " Price=", DoubleToString(result.price, Digits),
              " Lots=", DoubleToString(result.lotSize, 2));
    }
    else
    {
        Print("ORDER FAILED: ", result.errorMessage, " (Code: ", result.errorCode, ")");
    }
}

//+------------------------------------------------------------------+
//| Handle order error                                               |
//+------------------------------------------------------------------+
void OrderManager::HandleOrderError(int errorCode, OrderResult& result)
{
    result.errorCode = errorCode;
    
    switch(errorCode)
    {
        case ERR_NOT_ENOUGH_MONEY:
            result.errorMessage = "Not enough money";
            break;
        case ERR_INVALID_TRADE_VOLUME:
            result.errorMessage = "Invalid trade volume";
            break;
        case ERR_TRADE_NOT_ALLOWED:
            result.errorMessage = "Trade not allowed";
            break;
        case ERR_INVALID_PRICE:
            result.errorMessage = "Invalid price";
            break;
        case ERR_REQUOTE:
            result.errorMessage = "Requote";
            break;
        case ERR_TRADE_CONTEXT_BUSY:
            result.errorMessage = "Trade context busy";
            break;
        default:
            result.errorMessage = "Unknown error: " + IntegerToString(errorCode);
            break;
    }
}

#endif // ORDER_MANAGER_MQH
